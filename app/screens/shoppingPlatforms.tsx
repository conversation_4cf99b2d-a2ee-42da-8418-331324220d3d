import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, TextInput, StyleSheet, ScrollView, Alert, Modal } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import i18n from '../i18n';
import { useSettings } from '../context/SettingsContext';
import * as Localization from 'expo-localization';

interface ShoppingPlatform {
    id: number;
    name: string;
    icon: string;
    isDefault: boolean;
    createdAt: string;
}

// 中国区默认购物平台
const DEFAULT_CHINA_PLATFORMS: Omit<ShoppingPlatform, 'id' | 'createdAt'>[] = [
    { name: '抖音', icon: '🎵', isDefault: true },
    { name: '闲鱼', icon: '🐟', isDefault: true },
    { name: '快团团', icon: '🛒', isDefault: true },
    { name: '淘宝', icon: '🛍️', isDefault: true },
];

const ShoppingPlatforms = () => {
    const [platforms, setPlatforms] = useState<ShoppingPlatform[]>([]);
    const [showAddModal, setShowAddModal] = useState(false);
    const [editingPlatform, setEditingPlatform] = useState<ShoppingPlatform | null>(null);
    const [platformName, setPlatformName] = useState('');
    const [platformIcon, setPlatformIcon] = useState('🛒');
    const { language } = useSettings();

    useEffect(() => {
        loadPlatforms();
    }, []);

    const loadPlatforms = async () => {
        // TODO: 从数据库加载购物平台数据
        // 检查是否为中国区用户
        const isChinaRegion = checkIsChinaRegion();
        
        if (isChinaRegion && platforms.length === 0) {
            // 为中国区用户初始化默认平台
            const defaultPlatforms = DEFAULT_CHINA_PLATFORMS.map((platform, index) => ({
                ...platform,
                id: index + 1,
                createdAt: new Date().toISOString(),
            }));
            setPlatforms(defaultPlatforms);
        }
    };

    const checkIsChinaRegion = () => {
        const locales = Localization.getLocales();
        const primaryLocale = locales[0];
        return primaryLocale?.languageTag?.toLowerCase().includes('cn') || 
               primaryLocale?.languageTag?.toLowerCase().includes('zh') ||
               language === 'zh';
    };

    const handleAddPlatform = async () => {
        if (!platformName.trim()) {
            Alert.alert('错误', '请输入平台名称');
            return;
        }

        try {
            const newPlatform: ShoppingPlatform = {
                id: Date.now(),
                name: platformName.trim(),
                icon: platformIcon,
                isDefault: false,
                createdAt: new Date().toISOString(),
            };

            if (editingPlatform) {
                // 更新现有平台
                setPlatforms(prev => prev.map(platform => 
                    platform.id === editingPlatform.id 
                        ? { ...newPlatform, id: editingPlatform.id, createdAt: editingPlatform.createdAt, isDefault: editingPlatform.isDefault }
                        : platform
                ));
            } else {
                // 添加新平台
                setPlatforms(prev => [...prev, newPlatform]);
            }

            resetForm();
            setShowAddModal(false);
        } catch (error) {
            console.error('Failed to save platform:', error);
            Alert.alert('错误', '保存失败');
        }
    };

    const handleEditPlatform = (platform: ShoppingPlatform) => {
        if (platform.isDefault) {
            Alert.alert('提示', '默认平台不能编辑');
            return;
        }
        setEditingPlatform(platform);
        setPlatformName(platform.name);
        setPlatformIcon(platform.icon);
        setShowAddModal(true);
    };

    const handleDeletePlatform = (platformId: number) => {
        const platform = platforms.find(p => p.id === platformId);
        if (platform?.isDefault) {
            Alert.alert('提示', '默认平台不能删除');
            return;
        }

        Alert.alert(
            '确认删除',
            '确定要删除这个购物平台吗？',
            [
                { text: '取消', style: 'cancel' },
                {
                    text: '删除',
                    style: 'destructive',
                    onPress: () => {
                        setPlatforms(prev => prev.filter(platform => platform.id !== platformId));
                    }
                }
            ]
        );
    };

    const resetForm = () => {
        setPlatformName('');
        setPlatformIcon('🛒');
        setEditingPlatform(null);
    };

    const renderPlatform = (platform: ShoppingPlatform) => (
        <View key={platform.id} style={styles.platformItem}>
            <View style={styles.platformHeader}>
                <View style={styles.platformIcon}>
                    <Text style={styles.platformIconText}>{platform.icon}</Text>
                </View>
                <View style={styles.platformInfo}>
                    <Text style={styles.platformName}>{platform.name}</Text>
                    {platform.isDefault && (
                        <Text style={styles.defaultLabel}>默认</Text>
                    )}
                </View>
                {!platform.isDefault && (
                    <View style={styles.platformActions}>
                        <TouchableOpacity
                            style={styles.actionButton}
                            onPress={() => handleEditPlatform(platform)}
                        >
                            <Ionicons name="pencil-outline" size={20} color="#666" />
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={styles.actionButton}
                            onPress={() => handleDeletePlatform(platform.id)}
                        >
                            <Ionicons name="trash-outline" size={20} color="#dc4446" />
                        </TouchableOpacity>
                    </View>
                )}
            </View>
        </View>
    );

    const renderAddModal = () => (
        <Modal
            visible={showAddModal}
            transparent={true}
            animationType="slide"
            onRequestClose={() => {
                resetForm();
                setShowAddModal(false);
            }}
        >
            <View style={styles.modalOverlay}>
                <View style={styles.modalContent}>
                    <View style={styles.modalHeader}>
                        <Text style={styles.modalTitle}>
                            {editingPlatform ? '编辑购物平台' : '添加购物平台'}
                        </Text>
                        <TouchableOpacity
                            onPress={() => {
                                resetForm();
                                setShowAddModal(false);
                            }}
                            style={styles.modalCloseButton}
                        >
                            <Ionicons name="close" size={24} color="#666" />
                        </TouchableOpacity>
                    </View>

                    <View style={styles.modalBody}>
                        <Text style={styles.inputLabel}>平台名称</Text>
                        <TextInput
                            style={styles.textInput}
                            placeholder="请输入平台名称"
                            value={platformName}
                            onChangeText={setPlatformName}
                        />

                        <Text style={styles.inputLabel}>图标</Text>
                        <View style={styles.iconSelector}>
                            {['🛒', '🛍️', '📱', '💻', '🏪', '🏬', '🎵', '🐟', '📦', '🎁'].map(icon => (
                                <TouchableOpacity
                                    key={icon}
                                    style={[
                                        styles.iconOption,
                                        platformIcon === icon && styles.selectedIcon
                                    ]}
                                    onPress={() => setPlatformIcon(icon)}
                                >
                                    <Text style={styles.iconText}>{icon}</Text>
                                </TouchableOpacity>
                            ))}
                        </View>
                    </View>

                    <View style={styles.modalActions}>
                        <TouchableOpacity
                            style={styles.cancelButton}
                            onPress={() => {
                                resetForm();
                                setShowAddModal(false);
                            }}
                        >
                            <Text style={styles.cancelButtonText}>取消</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={styles.confirmButton}
                            onPress={handleAddPlatform}
                        >
                            <Text style={styles.confirmButtonText}>
                                {editingPlatform ? '更新' : '添加'}
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    );

    return (
        <View style={styles.container}>
            <ScrollView style={styles.scrollView}>
                <TouchableOpacity
                    style={styles.addButton}
                    onPress={() => setShowAddModal(true)}
                >
                    <Ionicons name="add" size={24} color="white" />
                    <Text style={styles.addButtonText}>添加购物平台</Text>
                </TouchableOpacity>

                {platforms.length === 0 ? (
                    <View style={styles.emptyState}>
                        <Ionicons name="storefront-outline" size={64} color="#ccc" />
                        <Text style={styles.emptyText}>暂无购物平台</Text>
                        <Text style={styles.emptySubtext}>点击上方按钮添加您的购物平台</Text>
                    </View>
                ) : (
                    <View style={styles.platformsList}>
                        {platforms.map(renderPlatform)}
                    </View>
                )}
            </ScrollView>

            {renderAddModal()}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    scrollView: {
        flex: 1,
        padding: 16,
    },
    addButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#FF5722',
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 12,
        marginBottom: 20,
    },
    addButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '500',
        marginLeft: 8,
    },
    emptyState: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 60,
    },
    emptyText: {
        fontSize: 18,
        color: '#666',
        marginTop: 16,
        fontWeight: '500',
    },
    emptySubtext: {
        fontSize: 14,
        color: '#999',
        marginTop: 8,
        textAlign: 'center',
    },
    platformsList: {
        gap: 12,
    },
    platformItem: {
        backgroundColor: 'white',
        borderRadius: 12,
        padding: 16,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
    },
    platformHeader: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    platformIcon: {
        width: 48,
        height: 48,
        borderRadius: 24,
        backgroundColor: '#fff3e0',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 12,
    },
    platformIconText: {
        fontSize: 24,
    },
    platformInfo: {
        flex: 1,
    },
    platformName: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333',
        marginBottom: 4,
    },
    defaultLabel: {
        fontSize: 12,
        color: '#FF5722',
        backgroundColor: '#fff3e0',
        paddingHorizontal: 8,
        paddingVertical: 2,
        borderRadius: 4,
        alignSelf: 'flex-start',
    },
    platformActions: {
        flexDirection: 'row',
        gap: 8,
    },
    actionButton: {
        padding: 8,
        borderRadius: 8,
        backgroundColor: '#f5f5f5',
    },
    // Modal styles
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        backgroundColor: 'white',
        borderRadius: 16,
        width: '90%',
        maxWidth: 400,
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#333',
    },
    modalCloseButton: {
        padding: 4,
    },
    modalBody: {
        padding: 20,
    },
    inputLabel: {
        fontSize: 14,
        fontWeight: '500',
        color: '#333',
        marginBottom: 8,
        marginTop: 12,
    },
    textInput: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 12,
        fontSize: 16,
        backgroundColor: '#f9f9f9',
    },
    iconSelector: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 8,
        marginTop: 8,
    },
    iconOption: {
        width: 40,
        height: 40,
        borderRadius: 8,
        backgroundColor: '#f5f5f5',
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 2,
        borderColor: 'transparent',
    },
    selectedIcon: {
        borderColor: '#FF5722',
        backgroundColor: '#fff3e0',
    },
    iconText: {
        fontSize: 20,
    },
    modalActions: {
        flexDirection: 'row',
        padding: 20,
        gap: 12,
    },
    cancelButton: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        backgroundColor: '#f5f5f5',
        alignItems: 'center',
    },
    cancelButtonText: {
        fontSize: 16,
        color: '#666',
    },
    confirmButton: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        backgroundColor: '#FF5722',
        alignItems: 'center',
    },
    confirmButtonText: {
        fontSize: 16,
        color: 'white',
        fontWeight: '500',
    },
});

export default ShoppingPlatforms;
