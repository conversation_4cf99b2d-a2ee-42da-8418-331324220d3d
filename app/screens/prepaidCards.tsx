import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, TextInput, StyleSheet, ScrollView, Alert, Modal } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import i18n from '../i18n';
import { useSettings } from '../context/SettingsContext';

interface PrepaidCard {
    id: number;
    name: string;
    balance: number;
    rechargeDate: string;
    createdAt: string;
}

const PrepaidCards = () => {
    const [cards, setCards] = useState<PrepaidCard[]>([]);
    const [showAddModal, setShowAddModal] = useState(false);
    const [editingCard, setEditingCard] = useState<PrepaidCard | null>(null);
    const [cardName, setCardName] = useState('');
    const [cardBalance, setCardBalance] = useState('');
    const [rechargeDate, setRechargeDate] = useState(new Date().toISOString().split('T')[0]);
    const { currency } = useSettings();

    useEffect(() => {
        loadCards();
    }, []);

    const loadCards = async () => {
        // TODO: 从数据库加载充值卡数据
        // 暂时使用模拟数据
        setCards([]);
    };

    const handleAddCard = async () => {
        if (!cardName.trim()) {
            Alert.alert('错误', '请输入卡片名称');
            return;
        }

        const balance = parseFloat(cardBalance);
        if (isNaN(balance) || balance < 0) {
            Alert.alert('错误', '请输入有效的余额');
            return;
        }

        try {
            const newCard: PrepaidCard = {
                id: Date.now(),
                name: cardName.trim(),
                balance: balance,
                rechargeDate: rechargeDate,
                createdAt: new Date().toISOString(),
            };

            if (editingCard) {
                // 更新现有卡片
                setCards(prev => prev.map(card => 
                    card.id === editingCard.id 
                        ? { ...newCard, id: editingCard.id, createdAt: editingCard.createdAt }
                        : card
                ));
            } else {
                // 添加新卡片
                setCards(prev => [...prev, newCard]);
            }

            resetForm();
            setShowAddModal(false);
        } catch (error) {
            console.error('Failed to save card:', error);
            Alert.alert('错误', '保存失败');
        }
    };

    const handleEditCard = (card: PrepaidCard) => {
        setEditingCard(card);
        setCardName(card.name);
        setCardBalance(card.balance.toString());
        setRechargeDate(card.rechargeDate);
        setShowAddModal(true);
    };

    const handleDeleteCard = (cardId: number) => {
        Alert.alert(
            '确认删除',
            '确定要删除这张充值卡吗？',
            [
                { text: '取消', style: 'cancel' },
                {
                    text: '删除',
                    style: 'destructive',
                    onPress: () => {
                        setCards(prev => prev.filter(card => card.id !== cardId));
                    }
                }
            ]
        );
    };

    const resetForm = () => {
        setCardName('');
        setCardBalance('');
        setRechargeDate(new Date().toISOString().split('T')[0]);
        setEditingCard(null);
    };

    const renderCard = (card: PrepaidCard) => (
        <View key={card.id} style={styles.cardItem}>
            <View style={styles.cardHeader}>
                <View style={styles.cardIcon}>
                    <Ionicons name="card" size={24} color="#E91E63" />
                </View>
                <View style={styles.cardInfo}>
                    <Text style={styles.cardName}>{card.name}</Text>
                    <Text style={styles.cardBalance}>
                        余额: {currency}{card.balance.toFixed(2)}
                    </Text>
                    <Text style={styles.cardDate}>
                        充值日期: {card.rechargeDate}
                    </Text>
                </View>
                <View style={styles.cardActions}>
                    <TouchableOpacity
                        style={styles.actionButton}
                        onPress={() => handleEditCard(card)}
                    >
                        <Ionicons name="pencil-outline" size={20} color="#666" />
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={styles.actionButton}
                        onPress={() => handleDeleteCard(card.id)}
                    >
                        <Ionicons name="trash-outline" size={20} color="#dc4446" />
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );

    const renderAddModal = () => (
        <Modal
            visible={showAddModal}
            transparent={true}
            animationType="slide"
            onRequestClose={() => {
                resetForm();
                setShowAddModal(false);
            }}
        >
            <View style={styles.modalOverlay}>
                <View style={styles.modalContent}>
                    <View style={styles.modalHeader}>
                        <Text style={styles.modalTitle}>
                            {editingCard ? '编辑充值卡' : '添加充值卡'}
                        </Text>
                        <TouchableOpacity
                            onPress={() => {
                                resetForm();
                                setShowAddModal(false);
                            }}
                            style={styles.modalCloseButton}
                        >
                            <Ionicons name="close" size={24} color="#666" />
                        </TouchableOpacity>
                    </View>

                    <View style={styles.modalBody}>
                        <Text style={styles.inputLabel}>卡片名称</Text>
                        <TextInput
                            style={styles.textInput}
                            placeholder="请输入卡片名称"
                            value={cardName}
                            onChangeText={setCardName}
                        />

                        <Text style={styles.inputLabel}>余额</Text>
                        <TextInput
                            style={styles.textInput}
                            placeholder="请输入余额"
                            value={cardBalance}
                            onChangeText={setCardBalance}
                            keyboardType="decimal-pad"
                        />

                        <Text style={styles.inputLabel}>充值日期</Text>
                        <TextInput
                            style={styles.textInput}
                            placeholder="YYYY-MM-DD"
                            value={rechargeDate}
                            onChangeText={setRechargeDate}
                        />
                    </View>

                    <View style={styles.modalActions}>
                        <TouchableOpacity
                            style={styles.cancelButton}
                            onPress={() => {
                                resetForm();
                                setShowAddModal(false);
                            }}
                        >
                            <Text style={styles.cancelButtonText}>取消</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={styles.confirmButton}
                            onPress={handleAddCard}
                        >
                            <Text style={styles.confirmButtonText}>
                                {editingCard ? '更新' : '添加'}
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    );

    return (
        <View style={styles.container}>
            <ScrollView style={styles.scrollView}>
                <TouchableOpacity
                    style={styles.addButton}
                    onPress={() => setShowAddModal(true)}
                >
                    <Ionicons name="add" size={24} color="white" />
                    <Text style={styles.addButtonText}>添加充值卡</Text>
                </TouchableOpacity>

                {cards.length === 0 ? (
                    <View style={styles.emptyState}>
                        <Ionicons name="card-outline" size={64} color="#ccc" />
                        <Text style={styles.emptyText}>暂无充值卡</Text>
                        <Text style={styles.emptySubtext}>点击上方按钮添加您的第一张充值卡</Text>
                    </View>
                ) : (
                    <View style={styles.cardsList}>
                        {cards.map(renderCard)}
                    </View>
                )}
            </ScrollView>

            {renderAddModal()}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    scrollView: {
        flex: 1,
        padding: 16,
    },
    addButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#E91E63',
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 12,
        marginBottom: 20,
    },
    addButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '500',
        marginLeft: 8,
    },
    emptyState: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 60,
    },
    emptyText: {
        fontSize: 18,
        color: '#666',
        marginTop: 16,
        fontWeight: '500',
    },
    emptySubtext: {
        fontSize: 14,
        color: '#999',
        marginTop: 8,
        textAlign: 'center',
    },
    cardsList: {
        gap: 12,
    },
    cardItem: {
        backgroundColor: 'white',
        borderRadius: 12,
        padding: 16,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
    },
    cardHeader: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    cardIcon: {
        width: 48,
        height: 48,
        borderRadius: 24,
        backgroundColor: '#fce4ec',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 12,
    },
    cardInfo: {
        flex: 1,
    },
    cardName: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333',
        marginBottom: 4,
    },
    cardBalance: {
        fontSize: 14,
        color: '#E91E63',
        fontWeight: '500',
        marginBottom: 2,
    },
    cardDate: {
        fontSize: 12,
        color: '#666',
    },
    cardActions: {
        flexDirection: 'row',
        gap: 8,
    },
    actionButton: {
        padding: 8,
        borderRadius: 8,
        backgroundColor: '#f5f5f5',
    },
    // Modal styles
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        backgroundColor: 'white',
        borderRadius: 16,
        width: '90%',
        maxWidth: 400,
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#333',
    },
    modalCloseButton: {
        padding: 4,
    },
    modalBody: {
        padding: 20,
    },
    inputLabel: {
        fontSize: 14,
        fontWeight: '500',
        color: '#333',
        marginBottom: 8,
        marginTop: 12,
    },
    textInput: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 12,
        fontSize: 16,
        backgroundColor: '#f9f9f9',
    },
    modalActions: {
        flexDirection: 'row',
        padding: 20,
        gap: 12,
    },
    cancelButton: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        backgroundColor: '#f5f5f5',
        alignItems: 'center',
    },
    cancelButtonText: {
        fontSize: 16,
        color: '#666',
    },
    confirmButton: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        backgroundColor: '#E91E63',
        alignItems: 'center',
    },
    confirmButtonText: {
        fontSize: 16,
        color: 'white',
        fontWeight: '500',
    },
});

export default PrepaidCards;
