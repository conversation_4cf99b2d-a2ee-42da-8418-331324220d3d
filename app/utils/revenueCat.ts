import Purchases, {
  PurchasesOffering,
  PurchasesPackage,
  CustomerInfo,
  PurchasesError,
  LOG_LEVEL,
  PURCHASES_ERROR_CODE
} from 'react-native-purchases';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// RevenueCat API Keys (需要在 RevenueCat Dashboard 中获取)
const REVENUECAT_API_KEYS = {
  ios: 'appl_TeoaQwBLYJLDxBIWEKTlNLdTzYr', // 替换为实际的 iOS API Key
  // android: 'goog_YOUR_ANDROID_API_KEY_HERE', // 替换为实际的 Android API Key
};

// 产品标识符 - 这是苹果App Store Connect中的产品ID
// 用于购买时指定产品，但在RevenueCat中检查权限时使用ENTITLEMENT_IDS
export const PRODUCT_IDS = {
    PREMIUM: 'yourae.ninecentspremium', // 苹果的产品ID
};

// 权限标识符 - 这是RevenueCat Dashboard中配置的entitlement标识符
// 用于检查用户是否拥有高级版权限：customerInfo.entitlements.active[ENTITLEMENT_IDS.PREMIUM]
export const ENTITLEMENT_IDS = {
    PREMIUM: 'ninecents premium', // RevenueCat中的权限标识符，需要在RevenueCat Dashboard中配置
};

// 本地存储键
   const PREMIUM_STATUS_KEY = 'premium_status_revenuecat';

// 初始化状态
let isInitialized = false;
let initializationError: Error | null = null;

/**
 * 初始化 RevenueCat
 */
export const initializeRevenueCat = async (): Promise<boolean> => {
  try {
    if (isInitialized) {
      console.log('✅ RevenueCat already initialized');
      return true;
    }

    // 获取平台对应的 API Key
    const apiKey = Platform.select({
      ios: REVENUECAT_API_KEYS.ios,
      // android: REVENUECAT_API_KEYS.android,
      default: REVENUECAT_API_KEYS.ios,
    });

    if (!apiKey || apiKey.includes('YOUR_')) {
      console.warn('⚠️ RevenueCat API Key not configured properly');
      // 在开发环境中，我们可以继续使用模拟数据
      if (__DEV__) {
        isInitialized = true;
        return true;
      }
      throw new Error('RevenueCat API Key not configured');
    }

    // 配置 RevenueCat
    Purchases.setLogLevel(LOG_LEVEL.DEBUG);
    
    // 初始化 RevenueCat
    Purchases.configure({ apiKey });
    
    console.log('✅ RevenueCat initialized successfully');
    isInitialized = true;
    
    // 检查当前用户的购买状态
    await checkPremiumStatus();
    
    return true;
  } catch (error) {
    initializationError = error instanceof Error ? error : new Error('Unknown initialization error');
    console.error('❌ Failed to initialize RevenueCat:', initializationError.message);
    
    // 在开发环境中，即使初始化失败也允许继续
    if (__DEV__) {
      console.warn('⚠️ Using mock implementation in development');
      isInitialized = true;
      return true;
    }
    
    return false;
  }
};

/**
 * 获取可用的产品包
 */
export const getOfferings = async (): Promise<PurchasesOffering | null> => {
  try {
    if (!isInitialized) {
      await initializeRevenueCat();
    }

    // 在开发环境中，如果 API Key 未配置，返回模拟数据
    if (__DEV__ && initializationError) {
      console.warn('⚠️ Returning mock offering data');
      return null;
    }

    const offerings = await Purchases.getOfferings();
    
    if (offerings.current) {
      console.log('✅ Successfully loaded offerings');
      return offerings.current;
    } else {
      console.warn('⚠️ No current offering available');
      return null;
    }
  } catch (error) {
    console.error('❌ Failed to get offerings:', error);
    return null;
  }
};

/**
 * 购买产品
 */
export const purchasePackage = async (packageToPurchase: PurchasesPackage): Promise<boolean> => {
  try {
    if (!isInitialized) {
      await initializeRevenueCat();
    }

    // 在开发环境中，如果 API Key 未配置，模拟成功购买
    if (__DEV__ && initializationError) {
      console.warn('⚠️ Simulating successful purchase in development');
      await AsyncStorage.setItem(PREMIUM_STATUS_KEY, 'true');
      return true;
    }

    const { customerInfo } = await Purchases.purchasePackage(packageToPurchase);
    
    // 检查购买是否成功
    const isPremium = customerInfo.entitlements.active[ENTITLEMENT_IDS.PREMIUM] !== undefined;
    
    if (isPremium) {
      await AsyncStorage.setItem(PREMIUM_STATUS_KEY, 'true');
      console.log('✅ Purchase successful');
      return true;
    } else {
      console.warn('⚠️ Purchase completed but premium not activated');
      return false;
    }
  } catch (error) {
    const purchasesError = error as PurchasesError;
    
    if (purchasesError.code === PURCHASES_ERROR_CODE.PURCHASE_CANCELLED_ERROR) {
      console.log('ℹ️ User cancelled the purchase');
      return false;
    }
    
    console.error('❌ Purchase failed:', purchasesError.message);
    throw error;
  }
};

/**
 * 恢复购买
 */
export const restorePurchases = async (): Promise<boolean> => {
  try {
    if (!isInitialized) {
      await initializeRevenueCat();
    }

    // 在开发环境中，如果 API Key 未配置，检查本地存储
    if (__DEV__ && initializationError) {
      console.warn('⚠️ Checking local storage for premium status');
      const localStatus = await AsyncStorage.getItem(PREMIUM_STATUS_KEY);
      return localStatus === 'true';
    }

    const customerInfo = await Purchases.restorePurchases();

    // 检查是否有有效的高级版权限
    const isPremium = customerInfo.entitlements.active[ENTITLEMENT_IDS.PREMIUM] !== undefined;

    if (isPremium) {
      await AsyncStorage.setItem(PREMIUM_STATUS_KEY, 'true');
      console.log('✅ Premium purchase restored');
      return true;
    } else {
      await AsyncStorage.setItem(PREMIUM_STATUS_KEY, 'false');
      console.log('ℹ️ No premium purchase found');
      return false;
    }
  } catch (error) {
    console.error('❌ Failed to restore purchases:', error);
    return false;
  }
};

/**
 * 兑换促销码
 */
export const redeemPromotionalCode = async (promoCode: string): Promise<boolean> => {
  try {
    if (!isInitialized) {
      await initializeRevenueCat();
    }

    // 在开发环境中，如果 API Key 未配置，模拟兑换成功
    if (__DEV__ && initializationError) {
      console.warn('⚠️ Simulating successful promo code redemption in development');
      // 模拟一些常见的测试兑换码
      const validTestCodes = ['TEST2024', 'PREMIUM', 'FREE', 'NINECENTS'];
      if (validTestCodes.includes(promoCode.toUpperCase())) {
        await AsyncStorage.setItem(PREMIUM_STATUS_KEY, 'true');
        return true;
      } else {
        return false;
      }
    }

    // 使用RevenueCat的促销码兑换功能
    // 注意：这需要在RevenueCat Dashboard中配置相应的促销码
    await Purchases.presentCodeRedemptionSheet();

    // 兑换完成后重新获取客户信息
    const customerInfo = await Purchases.getCustomerInfo();

    // 检查兑换后是否获得了高级版权限
    const isPremium = customerInfo.entitlements.active[ENTITLEMENT_IDS.PREMIUM] !== undefined;

    if (isPremium) {
      await AsyncStorage.setItem(PREMIUM_STATUS_KEY, 'true');
      console.log('✅ Promotional code redeemed successfully');
      return true;
    } else {
      console.log('ℹ️ Promotional code redeemed but premium not activated');
      return false;
    }
  } catch (error) {
    const purchasesError = error as PurchasesError;

    if (purchasesError.code === PURCHASES_ERROR_CODE.PURCHASE_CANCELLED_ERROR) {
      console.log('ℹ️ User cancelled the promo code redemption');
      return false;
    }

    console.error('❌ Failed to redeem promotional code:', purchasesError.message);
    throw error;
  }
};

/**
 * 检查高级版状态
 */
export const checkPremiumStatus = async (): Promise<boolean> => {
  try {
    // 首先检查本地存储
    const localStatus = await AsyncStorage.getItem(PREMIUM_STATUS_KEY);
    
    if (!isInitialized) {
      console.log('ℹ️ RevenueCat not initialized, using local status');
      return localStatus === 'true';
    }

    // 在开发环境中，如果 API Key 未配置，使用本地存储
    if (__DEV__ && initializationError) {
      return localStatus === 'true';
    }

    // 从 RevenueCat 获取最新状态
    const customerInfo = await Purchases.getCustomerInfo();
    const isPremium = customerInfo.entitlements.active[ENTITLEMENT_IDS.PREMIUM] !== undefined;
    
    // 更新本地存储
    await AsyncStorage.setItem(PREMIUM_STATUS_KEY, isPremium ? 'true' : 'false');
    
    return isPremium;
  } catch (error) {
    console.error('❌ Failed to check premium status:', error);
    
    // 如果网络请求失败，使用本地存储的状态
    const localStatus = await AsyncStorage.getItem(PREMIUM_STATUS_KEY);
    return localStatus === 'true';
  }
};

/**
 * 获取用户信息
 */
export const getCustomerInfo = async (): Promise<CustomerInfo | null> => {
  try {
    if (!isInitialized) {
      await initializeRevenueCat();
    }

    if (__DEV__ && initializationError) {
      console.warn('⚠️ Cannot get customer info in mock mode');
      return null;
    }

    return await Purchases.getCustomerInfo();
  } catch (error) {
    console.error('❌ Failed to get customer info:', error);
    return null;
  }
};

/**
 * 检查 RevenueCat 是否已正确初始化
 */
export const isRevenueCatReady = (): boolean => {
  return isInitialized && !initializationError;
};

/**
 * 获取初始化错误信息
 */
export const getInitializationError = (): Error | null => {
  return initializationError;
};
